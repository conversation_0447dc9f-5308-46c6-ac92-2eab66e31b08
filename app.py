"""
RCM to PFD Streamlit Application

This application processes Risk Control Matrix (RCM) Excel files to generate
Process Flow Diagrams (PFDs) using LLM technology.
"""

import streamlit as st
import streamlit.components.v1 as components
import json
import base64
import os
from io import BytesIO
from dotenv import load_dotenv

# Import custom modules
from rcm_analyzer import load_rcm_data, analyze_rcm_data
from bpmn_generator import generate_all_bpmn_diagrams

# Load environment variables
load_dotenv()

# Set page configuration
st.set_page_config(
    page_title="RCM to PFD Converter",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1E3A8A;
        margin-bottom: 1rem;
    }
    .sub-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #1E3A8A;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .info-box {
        background-color: #F0F7FF;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 5px solid #1E3A8A;
        margin-bottom: 1rem;
    }
    .success-box {
        background-color: #F0FFF4;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 5px solid #22543D;
        margin-bottom: 1rem;
    }
    .warning-box {
        background-color: #FFFAF0;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 5px solid #C05621;
        margin-bottom: 1rem;
    }
    /* SVG styling */
    svg {
        width: 100%;
        height: auto;
        max-width: 1200px;
        margin: 0 auto;
        display: block;
        border: 1px solid #eee;
        border-radius: 5px;
        background-color: white;
    }

    /* BPMN Viewer styling */
    .bpmn-viewer-container {
        width: 100%;
        height: 793px; /* A4 landscape height at 96 DPI */
        border: 1px solid #ccc;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
        background-color: white;
        /* A4 landscape aspect ratio (297mm × 210mm) */
        max-width: 1122px; /* A4 landscape width at 96 DPI */
        margin-left: auto;
        margin-right: auto;
    }

    .bpmn-controls {
        position: absolute;
        bottom: 10px;
        right: 10px;
        z-index: 10;
        background: rgba(255, 255, 255, 0.8);
        padding: 5px;
        border-radius: 3px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
    }

    .bpmn-controls button {
        margin: 0 5px;
        padding: 5px 10px;
        background: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 3px;
        cursor: pointer;
    }

    .bpmn-controls button:hover {
        background: #e9ecef;
    }

    /* Download links styling */
    .download-links {
        display: flex;
        justify-content: space-around;
        margin: 1rem 0;
    }

    .download-links a {
        display: inline-block;
        padding: 0.5rem 1rem;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 3px;
        text-decoration: none;
        color: #1E3A8A;
    }

    .download-links a:hover {
        background-color: #e9ecef;
    }
</style>
""", unsafe_allow_html=True)

def download_image(image, filename):
    """
    Create a download link for an image.

    Args:
        image: PIL Image
        filename: Name for the downloaded file

    Returns:
        str: HTML for the download link
    """
    buffered = BytesIO()
    image.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode()
    href = f'<a href="data:file/png;base64,{img_str}" download="{filename}.png">Download {filename} Diagram (PNG)</a>'
    return href


def download_xml(xml_filename, display_name):
    """
    Create a download link for a BPMN XML file.

    Args:
        xml_filename: Path to the XML file
        display_name: Name to display in the download link

    Returns:
        str: HTML for the download link
    """
    try:
        if os.path.exists(xml_filename):
            with open(xml_filename, 'r') as f:
                xml_content = f.read()
                xml_str = base64.b64encode(xml_content.encode()).decode()
                href = f'<a href="data:application/xml;base64,{xml_str}" download="{os.path.basename(xml_filename)}">Download {display_name} BPMN XML</a>'
                return href
        return ""
    except Exception as e:
        print(f"Error creating XML download link: {str(e)}")
        return ""

def render_bpmn_viewer(xml_filename, container_id):
    """
    Render a BPMN diagram using bpmn.js viewer in Streamlit.

    Args:
        xml_filename: Path to the BPMN XML file
        container_id: ID for the HTML container

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if os.path.exists(xml_filename):
            with open(xml_filename, 'r') as f:
                xml_content = f.read()

                # Escape any special characters in the XML content to prevent JavaScript errors
                xml_content = xml_content.replace('\\', '\\\\').replace('`', '\\`').replace('${', '\\${')

                # Create HTML component with bpmn.js viewer
                html_content = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8" />
                    <style>
                        .bpmn-viewer-container {{
                            width: 100%;
                            height: 793px; /* A4 landscape height at 96 DPI */
                            border: 1px solid #ccc;
                            position: relative;
                            overflow: hidden;
                            /* A4 landscape aspect ratio (297mm × 210mm) */
                            max-width: 1122px; /* A4 landscape width at 96 DPI */
                            margin-left: auto;
                            margin-right: auto;
                            background-color: white;
                        }}
                        .bpmn-controls {{
                            position: absolute;
                            bottom: 10px;
                            right: 10px;
                            z-index: 1000;
                            background: rgba(255, 255, 255, 0.8);
                            padding: 5px;
                            border-radius: 3px;
                            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
                        }}
                        .bpmn-controls button {{
                            margin: 0 5px;
                            padding: 5px 10px;
                            background: #f8f9fa;
                            border: 1px solid #ddd;
                            border-radius: 3px;
                            cursor: pointer;
                        }}
                        .debug-info {{
                            margin-top: 10px;
                            padding: 10px;
                            background: #f8f9fa;
                            border: 1px solid #ddd;
                            border-radius: 3px;
                            font-family: monospace;
                            font-size: 12px;
                            max-height: 200px;
                            overflow-y: auto;
                        }}
                        .error-message {{
                            color: red;
                            font-weight: bold;
                        }}
                        .warning-message {{
                            color: orange;
                        }}
                        .success-message {{
                            color: green;
                        }}
                    </style>
                </head>
                <body>
                    <div id="{container_id}" class="bpmn-viewer-container"></div>
                    <div id="debug-info-{container_id}" class="debug-info">
                        <h4>BPMN Viewer Debug Information</h4>
                        <p>If the diagram doesn't render correctly, check the messages below:</p>
                    </div>

                    <script src="https://unpkg.com/jquery@3.6.0/dist/jquery.min.js"></script>
                    <script src="https://unpkg.com/bpmn-js@9.3.1/dist/bpmn-navigated-viewer.development.js"></script>
                    <script>
                        // Wait for the DOM to be fully loaded
                        $(document).ready(function() {{
                            try {{
                                // Debug info element
                                const debugInfo = document.getElementById('debug-info-{container_id}');

                                // Log function for debugging
                                function log(message, type = 'info') {{
                                    console.log(message);
                                    if (debugInfo) {{
                                        const className = type === 'error' ? 'error-message' :
                                                         type === 'warning' ? 'warning-message' :
                                                         type === 'success' ? 'success-message' : '';

                                        const msgElement = document.createElement('div');
                                        msgElement.className = className;
                                        msgElement.innerHTML = message;
                                        debugInfo.appendChild(msgElement);
                                    }}
                                }}

                                log('Initializing BPMN viewer...', 'info');

                                // Get the container element
                                const container = document.getElementById('{container_id}');
                                if (!container) {{
                                    log('Error: Container element not found!', 'error');
                                    return;
                                }}

                                log('Container found, creating viewer...', 'info');

                                // Create a new BpmnJS instance with additional configuration
                                const viewer = new BpmnJS({{
                                    container: container,
                                    width: '100%',
                                    height: '100%',
                                    additionalModules: [
                                        // Add any additional modules if needed
                                    ],
                                    moddleExtensions: {{
                                        // Add any moddle extensions if needed
                                    }}
                                }});

                                // Get the canvas for later use
                                const canvas = viewer.get('canvas');

                                log('Viewer created, importing XML...', 'info');

                                // Pre-process the BPMN XML to fix common issues
                                function preprocessBpmnXml(xml) {{
                                    // Log the XML size for debugging
                                    log('BPMN XML size: ' + xml.length + ' characters', 'info');

                                    // Return the processed XML
                                    return xml;
                                }}

                                // The BPMN XML content
                                const bpmnXML = preprocessBpmnXml(`{xml_content}`);

                                // Import the BPMN diagram with better error handling
                                viewer.importXML(bpmnXML)
                                    .then(function(result) {{
                                        log('XML imported successfully', 'success');

                                        // Check for warnings
                                        if (result && result.warnings && result.warnings.length > 0) {{
                                            log('Import completed with ' + result.warnings.length + ' warnings:', 'warning');
                                            result.warnings.forEach(function(warning, idx) {{
                                                log('Warning ' + (idx + 1) + ': ' + warning.message, 'warning');
                                            }});
                                        }}

                                        // Get the canvas and zoom to fit the diagram
                                        const canvas = viewer.get('canvas');

                                        // Add a slight delay before zooming to ensure the diagram is rendered
                                        setTimeout(function() {{
                                            try {{
                                                log('Zooming to fit viewport...', 'info');

                                                // Use simple zoom to fit viewport
                                                canvas.zoom('fit-viewport');

                                                // Add a slight delay and zoom again to ensure proper fit
                                                setTimeout(function() {{
                                                    canvas.zoom('fit-viewport');
                                                }}, 200);

                                                log('Zoom complete', 'success');
                                            }} catch (zoomError) {{
                                                log('Error during zoom: ' + zoomError.message, 'error');
                                            }}
                                        }}, 1000);

                                        // Add zoom controls
                                        log('Adding zoom controls...', 'info');
                                        const zoomControls = document.createElement('div');
                                        zoomControls.className = 'bpmn-controls';
                                        zoomControls.innerHTML = `
                                            <button id="zoom-in-{container_id}">+</button>
                                            <button id="zoom-fit-{container_id}">Fit</button>
                                            <button id="zoom-out-{container_id}">-</button>
                                            <button id="toggle-debug-{container_id}">Debug</button>
                                        `;
                                        container.appendChild(zoomControls);

                                        // Add event listeners to zoom buttons
                                        document.getElementById('zoom-in-{container_id}').addEventListener('click', function() {{
                                            try {{
                                                canvas.zoom(canvas.zoom() + 0.1);
                                            }} catch (e) {{
                                                log('Error zooming in: ' + e.message, 'error');
                                            }}
                                        }});

                                        document.getElementById('zoom-fit-{container_id}').addEventListener('click', function() {{
                                            try {{
                                                // Use simple zoom to fit viewport
                                                canvas.zoom('fit-viewport');
                                            }} catch (e) {{
                                                log('Error fitting to viewport: ' + e.message, 'error');
                                            }}
                                        }});

                                        document.getElementById('zoom-out-{container_id}').addEventListener('click', function() {{
                                            try {{
                                                canvas.zoom(canvas.zoom() - 0.1);
                                            }} catch (e) {{
                                                log('Error zooming out: ' + e.message, 'error');
                                            }}
                                        }});

                                        // Get the debug info element
                                        const debugInfo = document.getElementById('debug-info-{container_id}');

                                        // Toggle debug info visibility
                                        document.getElementById('toggle-debug-{container_id}').addEventListener('click', function() {{
                                            if (debugInfo && debugInfo.style.display === 'none') {{
                                                debugInfo.style.display = 'block';
                                            }} else if (debugInfo) {{
                                                debugInfo.style.display = 'none';
                                            }}
                                        }});

                                        // Initially hide debug info
                                        if (debugInfo) {{
                                            debugInfo.style.display = 'none';
                                        }}

                                        log('BPMN viewer initialization complete', 'success');
                                    }})
                                    .catch(function(err) {{
                                        log('Error importing BPMN diagram: ' + err.message, 'error');

                                        // Show detailed error information
                                        if (err.warnings && err.warnings.length) {{
                                            log('Warnings:', 'warning');
                                            err.warnings.forEach(function(warning, idx) {{
                                                log('Warning ' + (idx + 1) + ': ' + warning.message, 'warning');
                                            }});
                                        }}

                                        // Try to provide more helpful information about common errors
                                        if (err.message.includes('sourceRef') || err.message.includes('targetRef')) {{
                                            log('This error is likely caused by sequence flows referencing elements that do not exist or are not yet drawn.', 'error');
                                            log('Check that all sourceRef and targetRef attributes in sequence flows point to valid element IDs.', 'info');
                                        }}

                                        // Show debug info when there's an error
                                        if (debugInfo) {{
                                            debugInfo.style.display = 'block';
                                        }}
                                    }});
                            }} catch (e) {{
                                console.error('Error in BPMN viewer script:', e);
                                if (debugInfo) {{
                                    debugInfo.style.display = 'block';
                                    const errorMsg = document.createElement('div');
                                    errorMsg.className = 'error-message';
                                    errorMsg.innerHTML = 'Fatal error: ' + e.message;
                                    debugInfo.appendChild(errorMsg);
                                }}
                            }}
                        }});
                    </script>
                </body>
                </html>
                """

                # Display the HTML component using Streamlit's components
                components.html(html_content, height=850, scrolling=True)
                return True
        return False
    except Exception as e:
        print(f"Error rendering BPMN viewer: {str(e)}")
        return False

def main():
    """Main function to run the Streamlit application."""

    # Initialize session state for tracking application flow
    if 'rcm_data' not in st.session_state:
        st.session_state.rcm_data = None
    if 'analysis_complete' not in st.session_state:
        st.session_state.analysis_complete = False
    if 'subprocess_analyses' not in st.session_state:
        st.session_state.subprocess_analyses = None
    if 'diagrams_generated' not in st.session_state:
        st.session_state.diagrams_generated = False
    if 'diagrams' not in st.session_state:
        st.session_state.diagrams = None

    # Header
    st.markdown('<div class="main-header">RCM to Process Flow Diagram Converter</div>', unsafe_allow_html=True)

    # Introduction
    st.markdown("""
    <div class="info-box">
    <p>This application converts Risk Control Matrix (RCM) Excel files into Process Flow Diagrams (PFDs) using advanced LLM technology.</p>
    <p>Follow the step-by-step process:</p>
    <ol>
        <li>Upload your RCM Excel file</li>
        <li>Click "Analyze RCM" to process the data using LLM and pre-generate BPMN JSON structures</li>
        <li>Review the analysis results</li>
        <li>Click "Generate BPMN Visualizations" to create visual process flows from the pre-generated structures</li>
    </ol>
    <p><strong>Note:</strong> The application uses AI to identify subprocesses, lanes, tasks, risks, and controls from your RCM data.</p>
    </div>
    """, unsafe_allow_html=True)

    # File upload section
    st.markdown('<div class="sub-header">Step 1: Upload RCM Excel File</div>', unsafe_allow_html=True)

    uploaded_file = st.file_uploader("Choose an Excel file", type=["xlsx", "xls"])

    if uploaded_file is not None:
        try:
            # Only load the data, don't analyze yet
            if st.session_state.rcm_data is None:
                st.session_state.rcm_data = load_rcm_data(uploaded_file)
                st.session_state.analysis_complete = False
                st.session_state.diagrams_generated = False

            # Step 2: Analyze RCM Data
            st.markdown('<div class="sub-header">Step 2: Analyze RCM Data</div>', unsafe_allow_html=True)

            analyze_button = st.button("Analyze RCM", type="primary")

            if analyze_button or st.session_state.analysis_complete:
                if not st.session_state.analysis_complete:
                    with st.spinner("Analyzing RCM data using LLM..."):
                        # Analyze the RCM data using LLM
                        st.session_state.subprocess_analyses = analyze_rcm_data(st.session_state.rcm_data)
                        st.session_state.analysis_complete = True

                # Display analysis results
                st.markdown('<div class="success-box"><p>✅ RCM Analysis Complete! BPMN JSON structures have been pre-generated.</p></div>', unsafe_allow_html=True)

                # Show a sample of the analysis results
                st.markdown('<div class="sub-header">Analysis Results</div>', unsafe_allow_html=True)

                if st.session_state.subprocess_analyses:
                    # Display the number of subprocesses identified
                    subprocesses = list(st.session_state.subprocess_analyses.keys())
                    st.write(f"**{len(subprocesses)} Subprocesses Identified:**")

                    # Create a DataFrame to display subprocess information
                    subprocess_info = []
                    for subprocess in subprocesses:
                        subprocess_data = st.session_state.subprocess_analyses[subprocess]

                        # Extract lane count
                        lane_count = 0
                        task_count = 0
                        risk_count = 0
                        control_count = 0

                        if isinstance(subprocess_data, dict) and "lanes" in subprocess_data:
                            lane_count = len(subprocess_data["lanes"])

                            # Count tasks, risks, and controls
                            for lane in subprocess_data["lanes"]:
                                if "tasks" in lane:
                                    task_count += len(lane["tasks"])

                                    # Count risks and controls
                                    for task in lane["tasks"]:
                                        if "risks" in task:
                                            risk_count += len(task["risks"])
                                        if "controls" in task:
                                            control_count += len(task["controls"])

                        subprocess_info.append({
                            "Subprocess": subprocess,
                            "Lanes": lane_count,
                            "Tasks": task_count,
                            "Risks": risk_count,
                            "Controls": control_count
                        })

                    # Display subprocess information as a table
                    import pandas as pd
                    subprocess_df = pd.DataFrame(subprocess_info)
                    st.dataframe(subprocess_df, use_container_width=True)

                    # Show a sample of the first subprocess analysis
                    if subprocesses:
                        with st.expander("View Sample Analysis Data"):
                            st.json(st.session_state.subprocess_analyses[subprocesses[0]])

                        # Add information about subprocess validation and enrichment
                        with st.expander("Subprocess Validation and Enrichment"):
                            st.markdown("""
                            ### Subprocess Validation and Enrichment

                            After extracting subprocesses using LLM, the application performs additional validation and enrichment:

                            1. **Validation**: Ensures all subprocesses have meaningful names and represent distinct process areas

                            2. **Enrichment**:
                               - Adds process area prefixes to subprocess names when appropriate
                               - Ensures each subprocess has at least one lane
                               - Validates that each lane contains at least one task
                               - Confirms that risks and controls are properly associated with tasks

                            3. **Completeness Check**:
                               - Verifies that all required BPMN elements are present
                               - Adds default elements if any are missing
                               - Ensures the process flow is logical and complete

                            This ensures that the generated BPMN diagrams will be comprehensive and follow BPMN standards.
                            """)

                # Step 3: Generate BPMN Diagrams
                st.markdown('<div class="sub-header">Step 3: Generate BPMN Diagrams</div>', unsafe_allow_html=True)

                st.markdown("""
                <div class="info-box">
                <p>BPMN JSON structures have already been generated during the analysis phase.
                Click the button below to generate the final BPMN XML files and visualizations.</p>
                </div>
                """, unsafe_allow_html=True)

                generate_button = st.button("Generate BPMN Visualizations", type="primary")

                if generate_button or st.session_state.diagrams_generated:
                    if not st.session_state.diagrams_generated:
                        with st.spinner("Generating BPMN diagrams..."):
                            # Generate BPMN diagrams
                            st.session_state.diagrams = generate_all_bpmn_diagrams(st.session_state.subprocess_analyses)
                            st.session_state.diagrams_generated = True

                    # Display the diagrams
                    st.markdown('<div class="sub-header">Generated Process Flow Diagrams</div>', unsafe_allow_html=True)

                    if st.session_state.diagrams:
                        # Create tabs for each subprocess
                        tabs = st.tabs([f"Subprocess: {name}" for name in st.session_state.diagrams.keys()])

                        for i, (subprocess_name, diagram_info) in enumerate(st.session_state.diagrams.items()):
                            with tabs[i]:
                                # Get diagram information
                                image = diagram_info.get('image')
                                xml_file = diagram_info.get('xml_file')
                                json_file = diagram_info.get('json_file')
                                process_name = diagram_info.get('process_name', subprocess_name)

                                st.markdown(f"### BPMN Diagram for {process_name}")

                                # Display interactive BPMN viewer
                                if xml_file and os.path.exists(xml_file):
                                    container_id = f"bpmn_container_{subprocess_name.replace(' ', '_')}"
                                    bpmn_displayed = render_bpmn_viewer(xml_file, container_id)

                                    if not bpmn_displayed:
                                        st.error("Failed to render interactive BPMN viewer. Please check the XML file format.")
                                else:
                                    st.error("BPMN XML file not found. Please try regenerating the diagram.")

                                # Add note about warnings
                                st.info("""
                                **Note about warnings**: You may see warnings about unrecognized elements like
                                `textAnnotation`, `association`, or `dataStore`. These are expected and don't
                                affect the diagram's functionality. The warnings occur because these elements
                                are part of the BPMN 2.0 specification but are implemented differently in bpmn.js.
                                """)


                                # Add download buttons
                                st.markdown("### Download Options")
                                download_links = '<div class="download-links">'

                                # Add XML download link if available
                                if xml_file and os.path.exists(xml_file):
                                    xml_link = download_xml(xml_file, f"BPMN_{process_name.replace(' ', '_')}")
                                    if xml_link:
                                        download_links += xml_link

                                # Add PNG download link if image is available
                                if image:
                                    download_links += download_image(image, f"BPMN_{process_name.replace(' ', '_')}")

                                download_links += '</div>'
                                st.markdown(download_links, unsafe_allow_html=True)

                                # Display the structured data
                                with st.expander("View Structured Data"):
                                    st.json(st.session_state.subprocess_analyses[subprocess_name])

                                # Display JSON structure if available
                                if json_file and os.path.exists(json_file):
                                    with st.expander("View BPMN JSON Structure"):
                                        with open(json_file, 'r') as f:
                                            bpmn_structure = json.load(f)
                                            st.json(bpmn_structure)

                        # Success message
                        st.markdown("""
                        <div class="success-box">
                        <p>✅ Diagram generation complete! You can view and download the generated diagrams above.</p>
                        </div>
                        """, unsafe_allow_html=True)
                    else:
                        st.markdown("""
                        <div class="warning-box">
                        <p>No diagrams were generated. Please check your RCM data format and try again.</p>
                        </div>
                        """, unsafe_allow_html=True)

        except Exception as e:
            # Error message
            st.error(f"An error occurred: {str(e)}")
            st.markdown("""
            <div class="warning-box">
            <p>An error occurred while processing your Excel file. Please ensure your file contains RCM data with columns for controls, risks, and responsible departments/roles.</p>
            </div>
            """, unsafe_allow_html=True)

            # Reset session state on error
            st.session_state.rcm_data = None
            st.session_state.analysis_complete = False
            st.session_state.subprocess_analyses = None
            st.session_state.diagrams_generated = False
            st.session_state.diagrams = None

    # Instructions section
    with st.expander("How to Use This Application"):
        st.markdown("""
        ### Instructions

        1. **Prepare your RCM Excel file**:
           - Include columns for risks, controls, and responsible departments/roles
           - The application will use AI to identify all elements regardless of column structure

        2. **Upload your file**:
           - Click the 'Browse files' button in Step 1 and select your Excel file

        3. **Analyze the RCM data**:
           - Click the 'Analyze RCM' button in Step 2
           - The application will use LLM to extract pools, lanes, tasks, risks, and controls
           - BPMN JSON structures are automatically generated during this phase
           - Review the analysis results before proceeding

        4. **Generate BPMN visualizations**:
           - Click the 'Generate BPMN Visualizations' button in Step 3
           - This will convert the pre-generated JSON structures into BPMN XML files and visualizations
           - Each subprocess will have its own tab with a BPMN diagram
           - The diagrams follow standard BPMN 2.0 notation as shown in Camunda examples
           - Lane names are formatted vertically along the left side of each lane
           - Tasks include detailed descriptions inside task boxes rather than just titles
           - Standard BPMN events, gateways, and sequence flows are used
           - Risks and controls are only associated with tasks when explicitly mentioned
           - Systems are represented as standard BPMN data store symbols with a legend

        5. **Download the diagrams**:
           - Use the download links below each diagram to save them as XML or PNG files

        ### About BPMN Diagrams

        Business Process Model and Notation (BPMN) is a standardized graphical representation for specifying business processes. The diagrams generated by this application follow standard BPMN 2.0 conventions:

        - **Pools**: Represent subprocesses (major process areas)
        - **Lanes**: Represent departments or roles responsible for activities with vertical lane names
        - **Tasks**: Represent activities or steps in the process with detailed descriptions
        - **Events**: Standard BPMN start and end events with proper notation
        - **Gateways**: Standard BPMN gateway notation (XOR, AND, OR) when decision points are present
        - **Sequence Flows**: Connect events and activities in the proper order
        - **Annotations**: Represent risks and controls associated with specific tasks
        - **Data Stores**: Represented as cylindrical database symbols for systems mentioned in the RCM data
        - **Legend**: Provides information about systems used in the process

        Key features of the generated diagrams:
        - Native BPMN 2.0 format following Camunda standard notation
        - Vertical lane names along the left side of each lane
        - Task descriptions inside task boxes rather than just titles
        - Risks and controls only associated with tasks when explicitly mentioned
        - Systems represented as standard BPMN data store symbols with a legend
        - Proper BPMN 2.0 notation for all diagram elements (tasks, gateways, events, etc.)

        These diagrams can be used for process documentation, risk assessment, and compliance purposes.
        """)

    # Footer
    st.markdown("""
    ---
    <p style="text-align: center; color: #718096; font-size: 0.8rem;">
    RCM to PFD Converter | Powered by OpenAI and Google Gemini
    </p>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
