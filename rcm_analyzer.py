"""
RCM Analyzer Module

This module handles the analysis of Risk Control Matrix (RCM) Excel files
using OpenAI's API to extract subprocess information and structure it for
BPMN diagram generation.
"""

import os
import pandas as pd
import json
from openai import OpenAI
from dotenv import load_dotenv
from bpmn_generator import generate_bpmn_json_structure

# Load environment variables
load_dotenv()

# Configure OpenAI API
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4o")
OPENAI_TEMPERATURE = float(os.getenv("OPENAI_TEMPERATURE", 0.2))
OPENAI_MAX_TOKENS = int(os.getenv("OPENAI_MAX_TOKENS", 8192))

def load_rcm_data(uploaded_file):
    """
    Load and parse the uploaded RCM Excel file.

    Args:
        uploaded_file: The uploaded Excel file from Streamlit

    Returns:
        DataFrame: Pandas DataFrame containing the RCM data
    """
    try:
        df = pd.read_excel(uploaded_file)
        return df
    except Exception as e:
        raise Exception(f"Error loading Excel file: {str(e)}")

def extract_subprocesses(df):
    """
    Extract unique subprocess names from the RCM data using LLM.

    Args:
        df: DataFrame containing the RCM data

    Returns:
        list: List of unique subprocess names
    """
    # Always use LLM to identify subprocesses, regardless of column structure
    return identify_subprocesses_with_llm(df)



def identify_subprocesses_with_llm(df):
    """
    Use OpenAI to identify subprocesses from the RCM data.

    Args:
        df: DataFrame containing the RCM data

    Returns:
        list: List of identified subprocess names
    """
    # Convert DataFrame to JSON for the prompt
    df_json = df.to_json(orient='records')

    # Create prompt for OpenAI
    prompt = f"""
    Analyze the following Risk Control Matrix (RCM) data and identify distinct subprocesses (Pools in BPMN terminology):

    {df_json}

    Your task is to perform a comprehensive analysis of the RCM data to identify all distinct subprocesses:

    1. Examine the entire dataset to identify logical process areas that would be represented as Pools in BPMN

    2. Consider the following sources of information:
       - Look for explicit subprocess or process columns if they exist
       - Analyze control descriptions to identify distinct business processes
       - Look for patterns in risk descriptions that indicate separate process areas
       - Consider department or role information that might indicate process boundaries
       - Identify logical groupings of controls and risks that form coherent business processes

    3. For each identified subprocess:
       - Ensure it represents a distinct and meaningful business process
       - Verify it has sufficient controls and risks to form a complete process flow
       - Confirm it would make sense as a standalone BPMN diagram

    Return a JSON array of subprocess names only, like this:
    ["Subprocess 1", "Subprocess 2", "Subprocess 3"]

    Important requirements:
    - Identify 3-7 distinct subprocesses (not too many, not too few)
    - Use clear, descriptive names that reflect the business process
    - Ensure each subprocess is distinct and represents a meaningful process area
    - Use your expertise as a GRC professional to identify logical process boundaries
    """

    # Call OpenAI API
    response = client.chat.completions.create(
        model=OPENAI_MODEL,
        messages=[
            {"role": "system", "content": "You are a GRC (Governance, Risk, and Compliance) expert specializing in SOX compliance and process analysis. Your task is to identify distinct subprocesses from RCM data."},
            {"role": "user", "content": prompt}
        ],
        temperature=OPENAI_TEMPERATURE,
        max_tokens=OPENAI_MAX_TOKENS
    )

    # Extract the response
    result = response.choices[0].message.content

    # Try to parse the JSON response
    try:
        import json
        import re

        # Look for JSON array in the response
        json_match = re.search(r'\[.*\]', result, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            subprocesses = json.loads(json_str)
            if subprocesses and len(subprocesses) > 0:
                return subprocesses

        # If no valid JSON array found, try to parse the entire response
        try:
            subprocesses = json.loads(result)
            if isinstance(subprocesses, list) and len(subprocesses) > 0:
                return subprocesses
            elif isinstance(subprocesses, dict):
                values = list(subprocesses.values())
                if values and len(values) > 0:
                    return values
        except:
            # If parsing fails, extract potential subprocess names using regex
            subprocess_matches = re.findall(r'"([^"]+)"', result)
            if subprocess_matches and len(subprocess_matches) > 0:
                return subprocess_matches

        # If all parsing attempts fail, return default subprocesses
        return ["Purchase Requisition", "Invoice Processing", "Payment Processing"]
    except Exception as e:
        print(f"Error parsing subprocess identification response: {str(e)}")
        # Return default subprocess names if parsing fails
        return ["Purchase Requisition", "Invoice Processing", "Payment Processing"]

def analyze_subprocess_data(df, subprocess_name):
    """
    Analyze all data related to a specific subprocess using LLM.

    Args:
        df: DataFrame containing the RCM data
        subprocess_name: Name of the subprocess to analyze

    Returns:
        dict: Structured data for the subprocess
    """
    # Always use LLM to identify and analyze subprocess data
    return identify_subprocess_data_with_llm(df, subprocess_name)

def identify_subprocess_data_with_llm(df, subprocess_name):
    """
    Use OpenAI to identify and analyze data relevant to a specific subprocess.

    Args:
        df: DataFrame containing the RCM data
        subprocess_name: Name of the subprocess to analyze

    Returns:
        dict: Structured data for the subprocess
    """
    # Convert DataFrame to JSON for the prompt
    df_json = df.to_json(orient='records')

    # Create prompt for OpenAI
    prompt = f"""
    Analyze the following Risk Control Matrix (RCM) data and extract all BPMN elements for the subprocess '{subprocess_name}':

    {df_json}

    Your task is to perform a comprehensive analysis of the RCM data and extract the following BPMN elements:

    1. POOL: The subprocess '{subprocess_name}' represents a Pool in BPMN terminology

    2. LANES: Identify departments, roles, or organizational units that are responsible for activities within this subprocess
       - Look for columns like "Department", "Role", "Owner", "Responsible", etc.
       - If such columns don't exist, infer the lanes from the control descriptions and activities
       - Each lane must contain at least one task
       - Lanes MUST ONLY represent process owners or departments (not systems or other entities)
       - Each pool MUST contain a MINIMUM of 2 lanes to properly represent process participants
       - There is NO MAXIMUM LIMIT on the number of lanes - include ALL relevant departments/roles identified in the RCM data
       - Do NOT assume start/end events as separate lanes

    3. TASKS: For each lane, identify all activities, steps, or procedures that are performed
       - Extract tasks from control descriptions, risk descriptions, or any other relevant fields
       - Arrange tasks in a logical sequence based on their dependencies
       - Assign each task a unique ID (e.g., T1, T2, T3)
       - Determine which tasks follow each task in the sequence (next_tasks)
       - CRITICAL: Include DETAILED TASK DESCRIPTIONS, not just task titles
         * Extract meaningful descriptions from the RCM data for each task with atleast 30 words.
         * Include specific actions, responsibilities, and context in the descriptions
         * Ensure descriptions are concise enough to fit within task boxes (50-100 characters)
         * Focus on the purpose and function of each task in the process
       - For each task, identify if there's a specific system or application mentioned

    4. RISKS AND CONTROLS: Identify risk-control pairs associated with SPECIFIC tasks
       - Extract risk and control information from related columns
       - If risk columns don't exist, infer risks from control descriptions
       - Each risk MUST be paired with exactly ONE control (1:1 relationship)
       - Assign sequential IDs to each risk-control pair (e.g., R1/C1, R2/C2, R3/C3)
       - ONLY associate risk-control pairs with tasks when they are explicitly mentioned or clearly relevant
       - Do NOT associate every risk-control pair with every task
       - Risks will be represented as RED TRIANGLE-SHAPED elements with ONLY the risk ID inside (no description)
       - Controls will be represented as GREEN DIAMOND-SHAPED elements with ONLY the control ID inside (no description)
       - The risk ID and its corresponding control ID must always use the same number (R1 pairs with C1, R2 with C2, etc.)

    6. SYSTEMS: Identify all systems or applications mentioned in the RCM data
       - Look for mentions of IT systems, applications, databases, or software
       - Assign each system a unique ID (e.g., S1, S2, S3)
       - ONLY associate systems with SPECIFIC tasks where they are EXPLICITLY mentioned or used
       - Do NOT automatically add system references to every task
       - A task should only have a system associated with it if the RCM data clearly indicates that system is used for that specific task

    Return the structured data in the following JSON format:
    {{
        "subprocess_name": "{subprocess_name}",
        "lanes": [
            {{
                "lane_name": "Department/Role name",
                "tasks": [
                    {{
                        "task_id": "T1",
                        "task_name": "Description of the task",
                        "task_description": "Detailed description of what the task involves",
                        "next_tasks": ["T2", "T3"],
                        "risks": [
                            {{
                                "risk_id": "PA.R01",
                                "risk_description": "Description of the risk"
                            }}
                        ],
                        "controls": [
                            {{
                                "control_id": "PA.C01",
                                "control_description": "Description of the control",
                                "related_risk_id": "PA.R01"
                            }}
                        ],
                        "systems": [
                            {{
                                "system_id": "S1",
                                "system_name": "Name of the system or application",
                                "system_description": "Brief description of the system's role in this task"
                            }}
                        ]
                    }}
                ]
            }}
        ],
        "systems_legend": [
            {{
                "system_id": "S1",
                "system_name": "Name of the system or application",
                "system_description": "Brief description of the system"
            }}
        ]
    }}

    Important requirements:
    - Use your expertise as a GRC professional to identify all elements even if they're not explicitly labeled
    - Ensure the process flow is logical and complete
    - There is NO MAXIMUM LIMIT on the number of lanes - include all relevant departments/roles
    - Lanes MUST ONLY represent process owners or departments (not systems or other entities)
    - Each lane must contain at least one task
    - CRITICAL: Include DETAILED TASK DESCRIPTIONS inside task boxes, not just task titles
      * Extract meaningful descriptions from the RCM data that explain what the task involves
      * Include specific actions, responsibilities, and context in the descriptions
      * Ensure descriptions are concise enough to fit within task boxes (50-100 characters)
      * Focus on the purpose and function of each task in the process
    - Each risk MUST be paired with exactly ONE control (1:1 relationship)
    - Assign SEQUENTIAL IDs to each risk-control pair (e.g., R1/C1, R2/C2, R3/C3)
    - Represent risks as RED TRIANGLE-SHAPED elements with ONLY the risk ID inside (no description)
    - Represent controls as GREEN DIAMOND-SHAPED elements with ONLY the control ID inside (no description)
    - Position risk triangles at the RIGHT TOP CORNER of their associated tasks
    - Position control diamonds at the RIGHT BOTTOM CORNER of their associated tasks
    - The risk ID and its corresponding control ID must always use the same number (R1 pairs with C1, R2 with C2, etc.)
    - ONLY associate risk-control pairs with tasks when explicitly mentioned or clearly relevant
    - ONLY associate systems with SPECIFIC tasks where they are EXPLICITLY mentioned or used
    - Do NOT automatically add system references to every task
    - Identify systems/applications mentioned and represent them as database symbols ONLY for tasks that use them
    - Create a systems legend that lists all systems used in the process
    - The sequence of tasks should represent a coherent business process
    """

    # Call OpenAI API
    response = client.chat.completions.create(
        model=OPENAI_MODEL,
        messages=[
            {"role": "system", "content": "You are a GRC (Governance, Risk, and Compliance) expert specializing in SOX compliance and process analysis. Your task is to analyze RCM data and structure it for BPMN diagram generation."},
            {"role": "user", "content": prompt}
        ],
        temperature=OPENAI_TEMPERATURE,
        max_tokens=OPENAI_MAX_TOKENS
    )

    # Extract the response
    result = response.choices[0].message.content

    # Try to parse the JSON response
    try:
        import json
        import re

        # Look for JSON object in the response
        json_match = re.search(r'\{.*\}', result, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            parsed_result = json.loads(json_str)
            return parsed_result
        else:
            # If no JSON object found, try to parse the entire response
            try:
                parsed_result = json.loads(result)
                return parsed_result
            except:
                # If parsing fails, create a structured response manually
                return {
                    "subprocess_name": subprocess_name,
                    "lanes": [
                        {
                            "lane_name": "Default Lane",
                            "tasks": [
                                {
                                    "task_id": "T1",
                                    "task_name": "Default Task",
                                    "task_description": "This is a default task created when no tasks could be identified in the RCM data.",
                                    "next_tasks": [],
                                    "risks": [
                                        {
                                            "risk_id": "R1",
                                            "risk_description": "Default Risk"
                                        }
                                    ],
                                    "controls": [
                                        {
                                            "control_id": "C1",
                                            "control_description": "Default Control",
                                            "related_risk_id": "R1"
                                        }
                                    ],
                                    "systems": []
                                }
                            ]
                        }
                    ],
                    "systems_legend": []
                }
    except Exception as e:
        print(f"Error parsing subprocess data response: {str(e)}")
        # Return a default structured response
        return {
            "subprocess_name": subprocess_name,
            "lanes": [
                {
                    "lane_name": "Default Lane",
                    "tasks": [
                        {
                            "task_id": "T1",
                            "task_name": "Default Task",
                            "task_description": "This is a default task created when no tasks could be identified in the RCM data.",
                            "next_tasks": [],
                            "risks": [
                                {
                                    "risk_id": "R1",
                                    "risk_description": "Default Risk"
                                }
                            ],
                            "controls": [
                                {
                                    "control_id": "C1",
                                    "control_description": "Default Control",
                                    "related_risk_id": "R1"
                                }
                            ],
                            "systems": []
                        }
                    ]
                }
            ],
            "systems_legend": []
        }



def analyze_rcm_data(df):
    """
    Analyze the entire RCM dataset and structure it for BPMN generation.
    Also generates BPMN JSON structures for each subprocess during analysis.

    Args:
        df: DataFrame containing the RCM data

    Returns:
        dict: Structured data for all subprocesses, including BPMN JSON structures
    """
    # Extract unique subprocesses
    subprocesses = extract_subprocesses(df)

    # Validate and enrich subprocess list
    subprocesses = validate_and_enrich_subprocesses(df, subprocesses)

    # Analyze each subprocess
    subprocess_analyses = {}
    for subprocess in subprocesses:
        subprocess_data = analyze_subprocess_data(df, subprocess)

        # Generate BPMN JSON structure for the subprocess
        try:
            print(f"Pre-generating BPMN JSON structure for {subprocess}...")
            bpmn_json_structure = generate_bpmn_json_structure(subprocess_data)

            # Add the BPMN JSON structure to the subprocess data
            subprocess_data['bpmn_json_structure'] = bpmn_json_structure

            # Save the JSON structure for reference
            json_file = f"{subprocess.replace(' ', '_')}_structure.json"
            with open(json_file, 'w') as f:
                json.dump(bpmn_json_structure, f, indent=2)
            print(f"BPMN JSON structure saved to {json_file}")

            # Add the JSON file path to the subprocess data
            subprocess_data['bpmn_json_file'] = json_file
        except Exception as e:
            print(f"Error pre-generating BPMN JSON structure for {subprocess}: {str(e)}")
            # Continue without the BPMN JSON structure
            subprocess_data['bpmn_json_structure'] = None
            subprocess_data['bpmn_json_file'] = None

        subprocess_analyses[subprocess] = subprocess_data

    # Post-process the analyses to ensure completeness and consistency
    subprocess_analyses = post_process_analyses(subprocess_analyses)

    return subprocess_analyses

def validate_and_enrich_subprocesses(df, subprocesses):
    """
    Validate and enrich the list of subprocesses.

    Args:
        df: DataFrame containing the RCM data
        subprocesses: List of subprocess names

    Returns:
        list: Validated and enriched list of subprocess names
    """
    # If no subprocesses were found, create default ones based on the data
    if not subprocesses or len(subprocesses) == 0:
        # Try to extract process information from column names
        process_columns = [col for col in df.columns if 'process' in col.lower()]
        if process_columns:
            # Use values from process-related columns
            process_values = []
            for col in process_columns:
                values = df[col].dropna().unique().tolist()
                process_values.extend(values)

            if process_values:
                return list(set(process_values))

        # If still no subprocesses, create default ones
        return ["Purchase to Pay", "Order to Cash", "Financial Reporting"]

    # Ensure subprocess names are clear and descriptive
    enriched_subprocesses = []
    for subprocess in subprocesses:
        # Clean up subprocess name
        cleaned_name = subprocess.strip()

        # Add process area prefix if missing
        if len(cleaned_name) < 10 and "process" not in cleaned_name.lower():
            if "purchase" in cleaned_name.lower() or "requisition" in cleaned_name.lower():
                cleaned_name = "Purchase to Pay: " + cleaned_name
            elif "order" in cleaned_name.lower() or "sale" in cleaned_name.lower():
                cleaned_name = "Order to Cash: " + cleaned_name
            elif "finance" in cleaned_name.lower() or "report" in cleaned_name.lower():
                cleaned_name = "Financial Reporting: " + cleaned_name

        enriched_subprocesses.append(cleaned_name)

    return enriched_subprocesses

def post_process_analyses(subprocess_analyses):
    """
    Post-process the subprocess analyses to ensure completeness and consistency.
    Ensures each subprocess has at least one lane and proper structure.

    Args:
        subprocess_analyses: Dictionary of structured data for all subprocesses

    Returns:
        dict: Post-processed subprocess analyses
    """
    # Ensure each subprocess has at least one lane
    for subprocess_name, subprocess_data in subprocess_analyses.items():
        # Convert string to dict if needed
        if isinstance(subprocess_data, str):
            try:
                import json
                import re

                # Look for JSON object in the response
                json_match = re.search(r'\{.*\}', subprocess_data, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    subprocess_data = json.loads(json_str)
                else:
                    # If no JSON object found, try to parse the entire response
                    try:
                        subprocess_data = json.loads(subprocess_data)
                    except:
                        # If parsing fails, create a default structure
                        subprocess_data = {
                            "subprocess_name": subprocess_name,
                            "lanes": []
                        }
            except:
                # If all parsing attempts fail, create a default structure
                subprocess_data = {
                    "subprocess_name": subprocess_name,
                    "lanes": []
                }

        # Ensure subprocess_name is set correctly
        if isinstance(subprocess_data, dict):
            subprocess_data["subprocess_name"] = subprocess_name

        # Ensure lanes exist
        if isinstance(subprocess_data, dict) and ("lanes" not in subprocess_data or not subprocess_data["lanes"]):
            subprocess_data["lanes"] = [
                {
                    "lane_name": "Default Department",
                    "tasks": [
                        {
                            "task_id": "T1",
                            "task_name": f"Process {subprocess_name}",
                            "task_description": f"This task represents the processing of {subprocess_name}.",
                            "next_tasks": [],
                            "risks": [
                                {
                                    "risk_id": "R1",
                                    "risk_description": "Process execution risk"
                                }
                            ],
                            "controls": [
                                {
                                    "control_id": "C1",
                                    "control_description": "Standard process control",
                                    "related_risk_id": "R1"
                                }
                            ],
                            "systems": []
                        }
                    ]
                }
            ]

        # Ensure all lanes represent process owners or departments
        if isinstance(subprocess_data, dict) and "lanes" in subprocess_data:
            lanes = subprocess_data["lanes"]
            for lane in lanes:
                # Ensure lane name is descriptive and represents a department/role
                if not lane.get("lane_name") or lane.get("lane_name") == "Default Lane":
                    lane["lane_name"] = "Process Owner"

                # Ensure lane name doesn't represent a system
                lane_name = lane.get("lane_name", "").lower()
                if "system" in lane_name or "application" in lane_name or "database" in lane_name or "software" in lane_name:
                    # Rename to a more appropriate department name
                    lane["lane_name"] = "IT Department"

        # Ensure systems_legend exists
        if isinstance(subprocess_data, dict) and "systems_legend" not in subprocess_data:
            subprocess_data["systems_legend"] = []

        # Ensure each task has a task_description and systems array
        if isinstance(subprocess_data, dict) and "lanes" in subprocess_data:
            for lane in subprocess_data["lanes"]:
                if "tasks" in lane:
                    for task in lane["tasks"]:
                        if "task_description" not in task:
                            task["task_description"] = task.get("task_name", "Task description not available")
                        if "systems" not in task:
                            task["systems"] = []

        # Update the subprocess data
        subprocess_analyses[subprocess_name] = subprocess_data

    return subprocess_analyses
